import { ColorPicker } from "@/components/common/ColorPicker"
import { BackgroundIcon } from "@/components/icons/BackgroundIcon"
import { ColorIcon } from "@/components/icons/ColorIcon"
import type { NodeStyle, TextStyle } from "@/types/mindmap"
import {
  FontBoldIcon,
  FontItalicIcon,
  ListBulletIcon,
  StrikethroughIcon,
  TextIcon,
  UnderlineIcon
} from "@radix-ui/react-icons"
import * as Toggle from "@radix-ui/react-toggle"
import * as Toolbar from "@radix-ui/react-toolbar"

interface StylePanelProps {
  nodeId: string
  nodeX: number
  nodeY: number
  style: NodeStyle
  onStyleChange: (style: NodeStyle) => void
  onTextStyleApply?: (style: TextStyle) => void
}

const StylePanelComponent = ({
  nodeId: _nodeId,
  nodeX,
  nodeY,
  style,
  onStyleChange,
  onTextStyleApply
}: StylePanelProps) => {
  const updateStyle = (updates: Partial<NodeStyle>) => {
    const newStyle = { ...style, ...updates }
    onStyleChange(newStyle)
  }

  // 应用文本样式到选中的文本
  const applyTextStyle = (textStyle: TextStyle) => {
    if (onTextStyleApply) {
      onTextStyleApply(textStyle)
    } else {
      // 如果没有文本样式回调，则应用到整个节点
      updateStyle(textStyle)
    }
  }

  return (
    <div
      className="style-panel"
      style={{
        left: nodeX - 100,
        top: nodeY + 40,
        zIndex: 1001
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="style-panel-content">
        <Toolbar.Root className="style-toolbar">
          <Toggle.Root className="style-btn">
            <TextIcon />
          </Toggle.Root>

          {/* 粗体按钮 */}
          <Toggle.Root
            className={`style-btn ${
              style.fontWeight === "bold" ? "active" : ""
            }`}
            pressed={style.fontWeight === "bold"}
            onPressedChange={(pressed) => {
              const newWeight: "normal" | "bold" = pressed ? "bold" : "normal"
              applyTextStyle({ fontWeight: newWeight })
            }}
          >
            <FontBoldIcon />
          </Toggle.Root>

          {/* 斜体按钮 */}
          <Toggle.Root
            className={`style-btn ${
              style.fontStyle === "italic" ? "active" : ""
            }`}
            pressed={style.fontStyle === "italic"}
            onPressedChange={(pressed) => {
              applyTextStyle({
                fontStyle: pressed ? "italic" : "normal"
              })
            }}
          >
            <FontItalicIcon />
          </Toggle.Root>

          {/* 下划线按钮 */}
          <Toggle.Root
            className={`style-btn ${
              style.textDecoration === "underline" ? "active" : ""
            }`}
            pressed={style.textDecoration === "underline"}
            onPressedChange={(pressed) => {
              applyTextStyle({
                textDecoration: pressed ? "underline" : "none"
              })
            }}
          >
            <UnderlineIcon />
          </Toggle.Root>

          <Toggle.Root className="style-btn">
            <StrikethroughIcon />
          </Toggle.Root>

          {/* 文字颜色 */}
          <ColorPicker
            value={style.color}
            onChange={(color: string) => applyTextStyle({ color })}
            title="文字颜色"
            icon={<ColorIcon />}
          />

          {/* 背景颜色 */}
          <ColorPicker
            value={style.backgroundColor}
            onChange={(color: string) =>
              applyTextStyle({ backgroundColor: color })
            }
            title="背景颜色"
            icon={<BackgroundIcon />}
          />
          <Toggle.Root className="style-btn">
            <ListBulletIcon />
          </Toggle.Root>

          <Toggle.Root className="style-btn">
            <ListBulletIcon />
          </Toggle.Root>
        </Toolbar.Root>
      </div>
    </div>
  )
}

export const StylePanel = StylePanelComponent
