import { useEffect } from "react"

export const useKeyboardShortcuts = (
  selectedNodeId: string | null,
  deleteNodeWithChildren: (nodeId: string) => void
) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Delete" && selectedNodeId && selectedNodeId !== "root") {
        e.preventDefault()
        deleteNodeWithChildren(selectedNodeId)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => {
      document.removeEventListener("keydown", handleKeyDown)
    }
  }, [selectedNodeId, deleteNodeWithChildren])
}
