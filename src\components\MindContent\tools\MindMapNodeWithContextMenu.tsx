import * as ContextMenuPrimitive from "@radix-ui/react-context-menu"
import { PlusIcon } from "@radix-ui/react-icons"
import React from "react"
import type { MindMapNode as MindMapNodeType, NodeStyle } from "../../../types/mindmap"
import { MindMapNode } from "./MindMapNode"

// AI图标组件
const AIIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" />
  </svg>
)

interface MindMapNodeWithContextMenuProps {
  node: MindMapNodeType
  rootNode: MindMapNodeType
  isSelected: boolean
  isEditing: boolean
  editingText: string
  editingStyle: NodeStyle
  showStylePanel: boolean
  showAddButton: boolean
  isDragging: boolean
  mindMapNodes: Record<string, MindMapNodeType>
  onSelect: () => void
  onDoubleClick: (e?: React.MouseEvent) => void
  onContextMenu: (e: React.MouseEvent) => void
  onMouseDown: (e: React.MouseEvent) => void
  onEditingTextChange: (text: string) => void
  onSaveEdit: () => void
  onCancelEdit: () => void
  onStyleChange: (style: NodeStyle) => void
  onAddChild: () => void
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onToggleUnderline?: () => void
  onColorChange?: (color: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
  // Context menu actions
  onAddChildNode: (nodeId: string) => void
  onAddSiblingNode: (nodeId: string) => void
  onAddParentNode: (nodeId: string) => void
  onDeleteNode: (nodeId: string) => void
  onAIGenerate?: (nodeId: string, content: string) => void
}

export const MindMapNodeWithContextMenu = ({
  node,
  rootNode,
  isSelected,
  isEditing,
  editingText,
  editingStyle,
  showStylePanel,
  showAddButton,
  isDragging,
  // mindMapNodes,
  onSelect,
  onDoubleClick,
  onContextMenu,
  onMouseDown,
  onEditingTextChange,
  onSaveEdit,
  onCancelEdit,
  onStyleChange,
  onAddChild,
  onToggleBold,
  onToggleItalic,
  onToggleUnderline,
  onColorChange,
  onFontSizeChange,
  onTextAlignChange,
  onAddChildNode,
  onAddSiblingNode,
  onAddParentNode,
  onDeleteNode,
  onAIGenerate,
}: MindMapNodeWithContextMenuProps) => {
  return (
    <ContextMenuPrimitive.Root>
      <ContextMenuPrimitive.Trigger asChild>
        <div>
          <MindMapNode
            node={node}
            rootNode={rootNode}
            isSelected={isSelected}
            isEditing={isEditing}
            editingText={editingText}
            editingStyle={editingStyle}
            showStylePanel={showStylePanel}
            showAddButton={showAddButton}
            isDragging={isDragging}
            onSelect={onSelect}
            onDoubleClick={onDoubleClick}
            onContextMenu={onContextMenu}
            onMouseDown={onMouseDown}
            onEditingTextChange={onEditingTextChange}
            onSaveEdit={onSaveEdit}
            onCancelEdit={onCancelEdit}
            onStyleChange={onStyleChange}
            onAddChild={onAddChild}
            onToggleBold={onToggleBold}
            onToggleItalic={onToggleItalic}
            onToggleUnderline={onToggleUnderline}
            onColorChange={onColorChange}
            onFontSizeChange={onFontSizeChange}
            onTextAlignChange={onTextAlignChange}
          />
        </div>
      </ContextMenuPrimitive.Trigger>

      <ContextMenuPrimitive.Portal>
        <ContextMenuPrimitive.Content className="context-menu">
          {/* AI创作功能 - 只对根节点启用 */}
          <ContextMenuPrimitive.Item
            className={`context-menu-item ${
              node.id !== "root" ? "disabled" : ""
            }`}
            onSelect={() => {
              if (node.id === "root" && onAIGenerate) {
                onAIGenerate(node.id, node.text)
              }
            }}
            disabled={node.id !== "root"}
          >
            <span className="flex items-center">
              <AIIcon />
              <span style={{ marginLeft: "8px" }}>AI创作</span>
            </span>
            <span className="shortcut">▶</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item
            className={`context-menu-item ${
              node.level >= 2 ? "disabled" : ""
            }`}
            onSelect={() => {
              if (node.level < 2) {
                onAddChildNode(node.id)
              }
            }}
            disabled={node.level >= 2}
          >
            <span className="flex items-center">
              <PlusIcon className="icon" />
              新增子主题
            </span>
            <span className="shortcut">Tab</span>
          </ContextMenuPrimitive.Item>

          {/* 新增同级主题 - 只对分支节点启用 */}
          <ContextMenuPrimitive.Item
            className={`context-menu-item ${
              node.id === "root" ? "disabled" : ""
            }`}
            onSelect={() => {
              if (node.id !== "root") {
                onAddSiblingNode(node.id)
              }
            }}
            disabled={node.id === "root"}
          >
            <span className="flex items-center">
              <PlusIcon className="icon" />
              新增同级主题
            </span>
            <span className="shortcut">Enter</span>
          </ContextMenuPrimitive.Item>

          {/* 新增父主题 - 只对分支节点启用 */}
          <ContextMenuPrimitive.Item
            className={`context-menu-item ${
              node.id === "root" ? "disabled" : ""
            }`}
            onSelect={() => {
              if (node.id !== "root") {
                onAddParentNode(node.id)
              }
            }}
            disabled={node.id === "root"}
          >
            <span className="flex items-center">
              <PlusIcon className="icon" />
              新增父主题
            </span>
            <span className="shortcut">Shift + Tab</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            插入
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            编号
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            收起主题
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            选择主题
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            <span>复制主题</span>
            <span className="shortcut">Ctrl + C</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            <span>剪切</span>
            <span className="shortcut">Ctrl + X</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            <span>粘贴主题</span>
            <span className="shortcut">Ctrl + V</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item
            className={`context-menu-item ${
              node.id === "root" ? "disabled" : ""
            }`}
            onSelect={() => {
              if (node.id !== "root") {
                onDeleteNode(node.id)
              }
            }}
            disabled={node.id === "root"}
          >
            <span>删除</span>
            <span className="shortcut">Del</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            <span>删除当前主题</span>
            <span className="shortcut">Ctrl + Del</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            批量删除
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            <span>聚焦模式</span>
            <span className="shortcut">Ctrl + .</span>
          </ContextMenuPrimitive.Item>

          <ContextMenuPrimitive.Separator className="context-menu-separator" />

          <ContextMenuPrimitive.Item className="context-menu-item disabled" disabled>
            导出当前主题为图片
          </ContextMenuPrimitive.Item>
        </ContextMenuPrimitive.Content>
      </ContextMenuPrimitive.Portal>
    </ContextMenuPrimitive.Root>
  )
}
