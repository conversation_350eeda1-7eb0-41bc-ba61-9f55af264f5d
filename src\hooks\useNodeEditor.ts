import {
  cancelNodeEdit as cancelNodeEditAction,
  saveNodeEdit as saveNodeEditAction,
  setEditingStyle,
  setEditingText,
  setShowStylePanel,
  startEditNode as startEditNodeAction
} from "../store"
import { useAppDispatch, useAppSelector } from "../store/hooks"
import type { NodeStyle } from "../types/mindmap"

export const useNodeEditor = () => {
  const dispatch = useAppDispatch()
  const isEditingNode = useAppSelector((state) => state.editor.isEditingNode)
  const editingText = useAppSelector((state) => state.editor.editingText)
  const showStylePanel = useAppSelector((state) => state.editor.showStylePanel)
  const editingStyle = useAppSelector((state) => state.editor.editingStyle)

  // 开始编辑节点
  const startEditNode = (
    nodeId: string,
    nodeText: string,
    nodeStyle?: NodeStyle
  ) => {
    console.log("进入useNodeEditor开始编辑节点测试选中:", nodeId)

    dispatch(startEditNodeAction({ nodeId, text: nodeText, style: nodeStyle }))

    // 延迟选中文本，确保textarea已经渲染
    setTimeout(() => {
      const textarea = document.querySelector(
        ".mindmap-input"
      ) as HTMLTextAreaElement
      if (textarea) {
        textarea.focus()
        textarea.setSelectionRange(0, textarea.value.length) // 选中所有文本
      }
    }, 10)
  }

  // 保存节点编辑
  const saveNodeEdit = (onSave: (text: string, style: NodeStyle) => void) => {
    if (isEditingNode) {
      // 确保文本不为空，如果为空则使用默认文本
      const finalText = editingText.trim() || "新节点"
      console.log("保存样式:", editingStyle) // 调试信息
      onSave(finalText, { ...editingStyle })
    }
    dispatch(saveNodeEditAction())
    // 不要自动隐藏样式面板，让用户手动点击其他地方关闭
  }

  // 取消节点编辑
  const cancelNodeEdit = () => {
    dispatch(cancelNodeEditAction())
    // 不要自动隐藏样式面板
  }

  // 更新编辑样式
  const updateEditingStyle = (
    newStyle: NodeStyle,
    onUpdate?: (style: NodeStyle) => void
  ) => {
    dispatch(setEditingStyle(newStyle))
    // 如果提供了回调，立即保存到节点
    if (onUpdate) {
      onUpdate(newStyle)
    }
  }

  // 设置编辑文本
  const handleSetEditingText = (text: string) => {
    dispatch(setEditingText(text))
  }

  // 设置显示样式面板
  const handleSetShowStylePanel = (nodeId: string | null) => {
    dispatch(setShowStylePanel(nodeId))
  }

  return {
    isEditingNode,
    editingText,
    setEditingText: handleSetEditingText,
    showStylePanel,
    setShowStylePanel: handleSetShowStylePanel,
    editingStyle,
    startEditNode,
    saveNodeEdit,
    cancelNodeEdit,
    updateEditingStyle
  }
}
