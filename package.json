{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint src", "lint:fix": "eslint src --fix", "prettier:check": "prettier --check \"src/**/*\"", "prettier:fix": "prettier --write \"src/**/*\"", "check:all": "pnpm run lint && pnpm run prettier:check && pnpm run ts:check", "fix:all": "pnpm run lint:fix && pnpm run prettier:fix"}, "dependencies": {"@radix-ui/colors": "^3.0.0", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-toolbar": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "@reduxjs/toolkit": "^2.8.2", "@types/node": "^24.1.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-color": "^2.19.3", "react-dom": "^19.1.0", "react-redux": "^9.2.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.6", "@types/react-redux": "^7.1.34", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "3.6.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}