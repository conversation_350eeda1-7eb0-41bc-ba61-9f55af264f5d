import React from "react"
import { tabConfig } from "../config/constants"
import type { TabNavigationProps } from "../type/types"

export const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange
}) => {
  return (
    <nav className="tab-navigation">
      {tabConfig.map((tab) => (
        <button
          key={tab.id}
          className={`tab-btn ${activeTab === tab.id ? "active" : ""}`}
          onClick={() => onTabChange(tab.id)}
          aria-label={tab.label}
        >
          {tab.label}
        </button>
      ))}
    </nav>
  )
}
