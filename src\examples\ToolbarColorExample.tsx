import React, { useState } from "react"
import { ToolbarColor } from "@/components/common/ToolbarColor"
import { ColorIcon } from "@/components/icons/ColorIcon"

/**
 * ToolbarColor 组件使用示例
 * 展示如何使用 showTitle 属性来控制是否显示 title
 */
export const ToolbarColorExample: React.FC = () => {
  const [color1, setColor1] = useState("#ff0000")
  const [color2, setColor2] = useState("#00ff00")

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-bold">ToolbarColor 示例</h2>
      
      <div className="space-y-2">
        <h3 className="font-semibold">显示 Title (showTitle=true，默认值)</h3>
        <ToolbarColor
          label="字体颜色"
          value={color1}
          onChange={setColor1}
          icon={<ColorIcon />}
          showTitle={true}
        />
        <p className="text-sm text-gray-600">
          鼠标悬停时会显示 "字体颜色" 的提示
        </p>
      </div>

      <div className="space-y-2">
        <h3 className="font-semibold">隐藏 Title (showTitle=false)</h3>
        <ToolbarColor
          label="背景颜色"
          value={color2}
          onChange={setColor2}
          icon={<ColorIcon />}
          showTitle={false}
        />
        <p className="text-sm text-gray-600">
          鼠标悬停时不会显示任何提示
        </p>
      </div>

      <div className="space-y-2">
        <h3 className="font-semibold">当前颜色值</h3>
        <div className="flex space-x-4">
          <div className="flex items-center space-x-2">
            <span>字体颜色:</span>
            <div 
              className="w-6 h-6 border border-gray-300 rounded"
              style={{ backgroundColor: color1 }}
            />
            <span className="font-mono text-sm">{color1}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>背景颜色:</span>
            <div 
              className="w-6 h-6 border border-gray-300 rounded"
              style={{ backgroundColor: color2 }}
            />
            <span className="font-mono text-sm">{color2}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
