import * as Popover from "@radix-ui/react-popover"
import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"

interface TooltipPopoverButtonProps {
  icon: React.ReactNode
  tooltipText: string
  popoverContent: React.ReactNode
  className?: string
}

export const TooltipPopoverButton: React.FC<TooltipPopoverButtonProps> = ({
  icon,
  tooltipText,
  popoverContent,
  className = ""
}) => {
  const [popoverOpen, setPopoverOpen] = React.useState(false)

  return (
    <Tooltip.Provider>
      <Popover.Root open={popoverOpen} onOpenChange={setPopoverOpen}>
        {/* Tooltip 只在 Popover 未打开时工作 */}
        <Tooltip.Root open={popoverOpen ? false : undefined}>
          <Tooltip.Trigger asChild>
            <Popover.Trigger asChild>
              <button
                type="button"
                className={`icon-btn ${className}`}
                aria-label={tooltipText}
                onClick={() => setPopoverOpen((prev) => !prev)}
              >
                {icon}
              </button>
            </Popover.Trigger>
          </Tooltip.Trigger>

          <Tooltip.Content
            side="bottom"
            align="center"
            className="tooltip-content"
            sideOffset={5}
          >
            {tooltipText}
            <Tooltip.Arrow className="tooltip-arrow" />
          </Tooltip.Content>
        </Tooltip.Root>

        <Popover.Portal>
          <Popover.Content
            className="popover-content"
            side="bottom"
            align="start"
            sideOffset={4}
          >
            {popoverContent}
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </Tooltip.Provider>
  )
}
