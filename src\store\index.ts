import { configureStore } from "@reduxjs/toolkit"
import editorReducer from "./editorSlice"
import mindMapReducer from "./mindMapSlice"
import uiReducer from "./uiSlice"

export const store = configureStore({
  reducer: {
    mindMap: mindMapReducer,
    ui: uiReducer,
    editor: editorReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略action types的序列化检查
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"]
      }
    })
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// 导出所有actions
export * from "./editorSlice"
export * from "./mindMapSlice"
export * from "./uiSlice"
