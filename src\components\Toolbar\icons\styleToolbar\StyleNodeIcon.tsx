import React from "react"

export const StyleNodeIcon: React.FC = () => (
  <svg
    className="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="2331"
    width="15"
    height="15"
  >
    <path
      d="M896 128v320H384V128h512z m-64 64H448v192h384V192zM704 576a192 192 0 0 1 0 384H576a192 192 0 0 1 0-384h128z m0 64H576a128 128 0 0 0-9.6 255.68L576 896h128a128 128 0 0 0 9.6-255.68L704 640z"
      fill="#8a8a8a"
      p-id="2332"
    ></path>
    <path d="M128 256h320v64H128z" fill="#8a8a8a" p-id="2333"></path>
    <path d="M128 256h64v512H128z" fill="#8a8a8a" p-id="2334"></path>
    <path d="M128 768h320v64H128z" fill="#8a8a8a" p-id="2335"></path>
  </svg>
)
