.avatar-root {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #eee;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  font-size: 16px;
  font-weight: bold;
  color: #666;
}

.hovercard-content {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  width: 200px;
}

.hovercard-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hovercard-name {
  font-weight: bold;
  font-size: 14px;
}

.hovercard-email {
  font-size: 12px;
  color: #666;
}

.hovercard-button {
  margin-top: 8px;
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}
.hovercard-button:hover {
  background: #0056b3;
}

.hovercard-arrow {
  fill: white;
}
