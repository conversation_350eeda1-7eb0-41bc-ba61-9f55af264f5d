// 节点样式类型
export interface NodeStyle {
  fontSize: number
  fontFamily: string
  fontWeight: "normal" | "bold"
  fontStyle: "normal" | "italic"
  textDecoration: "none" | "underline"
  color: string
  backgroundColor: string
  borderColor: string
  borderWidth: number
  textAlign?: "left" | "center" | "right"
}

// 文本片段样式类型
export interface TextStyle {
  fontWeight?: "normal" | "bold"
  fontStyle?: "normal" | "italic"
  textDecoration?: "none" | "underline"
  color?: string
  backgroundColor?: string
}

// 富文本片段类型
export interface TextSegment {
  text: string
  style?: TextStyle
}

// 富文本内容类型
export interface RichTextContent {
  segments: TextSegment[]
}

// 思维导图节点类型
export interface MindMapNode {
  id: string
  text: string
  richText?: RichTextContent // 富文本内容，可选
  x: number
  y: number
  level: number
  parentId?: string
  children: string[]
  style?: NodeStyle
  collapsed?: boolean // 是否折叠子节点
}

// 右键菜单状态类型
export interface ContextMenuState {
  visible: boolean
  x: number
  y: number
  nodeId: string | null
}

// 拖拽偏移类型
export interface DragOffset {
  x: number
  y: number
}

// 视图偏移类型
export interface ViewOffset {
  x: number
  y: number
}
