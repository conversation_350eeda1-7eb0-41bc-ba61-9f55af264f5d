import { useEffect } from "react"

export const useGlobalClick = (
  selectedNodeId: string | null,
  isEditingNode: string | null,
  setSelectedNodeId: (nodeId: string | null) => void,
  cancelNodeEdit: () => void,
  hideContextMenu: () => void,
  setShowStylePanel: (nodeId: string | null) => void,
  setShowAddButton: (nodeId: string | null) => void
) => {
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement

      // 检查是否点击了节点浮动工具栏
      const isFloatingToolbar = target.closest(".node-floating-toolbar")

      // 检查是否点击了header工具栏
      const isHeaderToolbar = target.closest(".toolbar")

      // 检查是否点击了header导航
      const isHeaderNav = target.closest(".header-nav")

      // 检查是否点击了节点本身
      const isNode = target.closest(".mindmap-node")

      // 检查是否点击了加号按钮
      const isAddButton = target.closest(".node-add-btn")

      // 检查是否点击了右键菜单
      const isContextMenu = target.closest(".context-menu")

      // 如果点击的不是以上任何元素，则取消选中和编辑状态
      if (
        !isFloatingToolbar &&
        !isHeaderToolbar &&
        !isHeaderNav &&
        !isNode &&
        !isAddButton &&
        !isContextMenu
      ) {
        if (selectedNodeId || isEditingNode) {
          setSelectedNodeId(null)
          if (isEditingNode) {
            cancelNodeEdit()
          }
          hideContextMenu()
          setShowStylePanel(null)
          setShowAddButton(null)
        }
      }
    }

    document.addEventListener("click", handleGlobalClick, false)
    return () => {
      document.removeEventListener("click", handleGlobalClick, false)
    }
  }, [
    selectedNodeId,
    isEditingNode,
    cancelNodeEdit,
    hideContextMenu,
    setSelectedNodeId,
    setShowAddButton,
    setShowStylePanel
  ])
}
