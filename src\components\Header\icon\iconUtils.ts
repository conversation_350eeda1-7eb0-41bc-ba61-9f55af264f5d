import React from "react"
import { ShareIcon } from "../../icons/ShareIcon"
import { StarIcon } from "../../icons/StarIcon"
import { TagIcon } from "../../icons/TagIcon"
import { MoveIcon } from "../../icons/MoveIcon"
import { ShortcutIcon } from "../../icons/ShortcutIcon"

// 图标映射
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  share: ShareIcon,
  star: StarIcon,
  tag: TagIcon,
  move: MoveIcon,
  shortcut: ShortcutIcon
}

// 获取图标组件
export const getIconComponent = (iconName: string) => {
  return iconMap[iconName] || (() => React.createElement("span", null, "?"))
}
