import { useState } from "react"
import type { ContextMenuState } from "../types/mindmap"

export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    visible: false,
    x: 0,
    y: 0,
    nodeId: null
  })

  // 设置当前右键菜单的节点ID
  const setContextMenuNodeId = (nodeId: string | null) => {
    setContextMenu((prev) => ({
      ...prev,
      nodeId: nodeId
    }))
  }



  // 隐藏右键菜单
  const hideContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      nodeId: null
    })
  }

  return {
    contextMenu,
    setContextMenuNodeId,
    hideContextMenu
  }
}
