import React from "react"

import type { ToolbarColorProps } from "../Toolbar/type/types"
import { ColorPicker } from "./ColorPicker"

export const ToolbarColor: React.FC<ToolbarColorProps> = ({
  label,
  value,
  onChange,
  disabled,
  icon,
  showTitle = false
}) => (
  <div
    className={`toolbar-color-picker ${disabled ? "disabled" : ""}`}
    title={showTitle ? label : undefined}
    onClick={(e) => e.stopPropagation()}
  >
    <ColorPicker
      value={value}
      showTitle={showTitle}
      onChange={onChange}
      disabled={disabled}
      title={showTitle ? label : undefined}
      icon={icon}
    />
  </div>
)
