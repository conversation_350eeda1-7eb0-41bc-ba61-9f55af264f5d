@import "@radix-ui/colors/black-alpha.css";
@import "@radix-ui/colors/violet.css";
/* Header 容器样式 */
.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 48px;
  max-width: 100%;
}

/* 文件控制区域 */
.file-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0 8px;
  white-space: nowrap;
}

/* 标签导航 */
.tab-navigation {
  display: flex;
  align-items: center;
  gap: 0;
  flex: 1;
  justify-content: center;
}

.tab-btn {
  padding: 10px 16px;
  font-size: 14px;
  color: #374151;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
  white-space: nowrap;
}

.tab-btn:hover {
  background-color: #f3f4f6;
}

.tab-btn.active {
  color: #14b8a6;
  font-weight: 500;
  position: relative;
}

.tab-btn.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #14b8a6;
}

/* 用户控制区域 */
.user-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.share-btn {
  padding: 6px 12px;
  background-color: #14b8a6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
  white-space: nowrap;
}

.share-btn:hover {
  background-color: #0d9488;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f3f4f6;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.user-avatar:hover {
  background-color: #e5e7eb;
}

/* 图标按钮 */
.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;
}

.icon-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.icon-btn-sm {
  width: 28px;
  height: 28px;
}

.icon-btn-lg {
  width: 36px;
  height: 36px;
}

/* Popover 样式 */
.popover-content {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 260px;
  z-index: 1000;
}

.card-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-top,
.card-bottom {
  background: white;
  border-radius: 6px;
  padding: 12px;
}

.card-top-file {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.file-icon {
  font-size: 20px;
  color: #a78bfa;
}

.file-info {
  flex: 1;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6b7280;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background-color: #f3f4f6;
}

.action-icon {
  width: 12px;
  height: 12px;
}

.separator {
  width: 1px;
  height: 16px;
  background-color: #d1d5db;
  margin: 0 4px;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.folder-icon {
  font-size: 16px;
  color: #fbbf24;
}

.location-path {
  font-size: 12px;
  color: #1f2937;
  flex: 1;
}

.location-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6b7280;
  transition: background-color 0.15s ease;
}

.location-btn:hover {
  background-color: #f3f4f6;
}

.location-icon {
  width: 12px;
  height: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 8px;
  }

  .file-controls {
    gap: 4px;
  }

  .file-name {
    display: none;
  }

  .tab-navigation {
    gap: 0;
  }

  .tab-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .user-controls {
    gap: 4px;
  }

  .share-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}

button {
  all: unset;
}

.TooltipContent {
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 15px;
  line-height: 1;
  color: var(--violet-11);
  background-color: white;
  box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  user-select: none;
  animation-duration: 400ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
}
.TooltipContent[data-state="delayed-open"][data-side="top"] {
  animation-name: slideDownAndFade;
}
.TooltipContent[data-state="delayed-open"][data-side="right"] {
  animation-name: slideLeftAndFade;
}
.TooltipContent[data-state="delayed-open"][data-side="bottom"] {
  animation-name: slideUpAndFade;
}
.TooltipContent[data-state="delayed-open"][data-side="left"] {
  animation-name: slideRightAndFade;
}

.TooltipArrow {
  fill: white;
}

.IconButton {
  font-family: inherit;
  border-radius: 100%;
  height: 35px;
  width: 35px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--violet-11);
  background-color: white;
  box-shadow: 0 2px 10px var(--black-a7);
  user-select: none;
}
.IconButton:hover {
  background-color: var(--violet-3);
}
.IconButton:focus {
  box-shadow: 0 0 0 2px black;
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.popover-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  width: 260px;
  z-index: 1000;
  background: #f5f5f5;
}

.card-panel {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.card-panel .card-top,
.card-panel .card-bottom {
  border: 1px solid white;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  background: white;
}

/* 文件信息样式 */
.card-top-file {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.file-icon {
  font-size: 18px;
  color: #a78bfa; /* 浅紫色 */
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8px;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 4px 6px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.action-icon {
  width: 20px;
  height: 20px;
}

.separator {
  width: 1px;
  height: 10px;
  background-color: #d1d5db;
}

/* 位置信息样式 */
.location-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.folder-icon {
  font-size: 16px;
  color: #fbbf24; /* 黄色 */
  flex-shrink: 0;
}

.location-path {
  font-size: 12px;
  color: #1f2937;
  flex: 1;
  min-width: 0;
}

.location-actions {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 6px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s;
  text-align: left;
  width: 100%;
}

.location-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.location-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.card-btn {
  background: none;
  border: none;
  text-align: left;
  padding: 6px 8px;
  font-size: 13px;
  cursor: pointer;
  border-radius: 4px;
}

.card-btn:hover {
  background-color: #f1f5f9;
}

.ToolbarRoot {
  display: flex;
  padding-top: 10px;
  width: 100%;
  min-width: max-content;
  juestify-content: space-between;
  border-radius: 6px;
}

.ToolbarToggleItem {
  flex: 0 0 auto;
  display: inline-flex;
  font-size: 13px;
  line-height: 1;
  align-items: center;
  justify-content: center;
}

.ToolbarSeparator {
  width: 1px;
  background-color: gray;
  margin: 0 10px;
  height: 10px;
}
.ToolbarToggleItem .icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.tab-btn {
  padding: 10px;
  font-size: 14px;
  color: #374151;
  background: transparent;
  border: none;
  cursor: pointer;
  position: relative;
}

.tab-btn.active {
  font-weight: 500;
  color: #14b8a6;
  position: relative;
}

.tab-btn.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #14b8a6;
}
