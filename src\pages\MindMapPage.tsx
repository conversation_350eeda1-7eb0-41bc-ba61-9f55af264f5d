import { <PERSON><PERSON>, <PERSON>MapC<PERSON><PERSON>, Toolbar } from "@/components"
import { useContextMenu, useNodeEditor, useUI, useViewDrag } from "@/hooks"
import { useAIGeneration } from "@/hooks/useAIGeneration"
import { useGlobalClick } from "@/hooks/useGlobalClick"
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts"
import { useMindMap } from "@/hooks/useMindMap"
import { useNodeStyle } from "@/hooks/useNodeStyle"
import { useEffect, useMemo } from "react"

export const MindMapPage = () => {
  // 使用Redux状态管理的hooks
  const { activeTab, showAddButton, setActiveTab, setShowAddButton } = useUI()

  // 使用自定义 hooks
  const {
    mindMapNodes,
    selectedNodeId,
    setSelectedNodeId,
    addChildNode,
    addSiblingNode,
    addParentNode,
    deleteNode,
    deleteNodeWithChildren,
    clearNodeChildren,
    createNodeFast,
    updateNode,
    recalculateLayout,
    toggleNodeCollapse
    // importMindMap, // 暂时注释掉
  } = useMindMap()

  const {
    isEditingNode,
    editingText,
    setEditingText,
    editingRichText,
    setEditingRichText,
    showStylePanel,
    setShowStylePanel,
    editingStyle,
    startEditNode,
    saveNodeEdit,
    cancelNodeEdit,
    updateEditingStyle
  } = useNodeEditor()

  const {
    isDragging,
    viewOffset,
    handleNodeMouseDown,
    handleMouseMove,
    handleMouseUp
  } = useViewDrag()

  const { setContextMenuNodeId, hideContextMenu } = useContextMenu()

  // 抽离的逻辑hooks
  const styleHandlers = useNodeStyle(selectedNodeId, mindMapNodes, updateNode)
  const { handleAIGenerate } = useAIGeneration(
    updateNode,
    createNodeFast,
    recalculateLayout,
    clearNodeChildren
  )

  // 键盘快捷键和全局点击处理
  useKeyboardShortcuts(selectedNodeId, deleteNodeWithChildren)
  useGlobalClick(
    selectedNodeId,
    isEditingNode,
    setSelectedNodeId,
    cancelNodeEdit,
    hideContextMenu,
    setShowStylePanel,
    setShowAddButton
  )

  // 使用useMemo优化节点数量计算
  const nodeCount = useMemo(
    () => Object.keys(mindMapNodes).length,
    [mindMapNodes]
  )

  // 监听节点数量变化，延迟重新计算布局和连接线
  useEffect(() => {
    if (nodeCount > 1) {
      // 避免初始化时触发
      const timer = setTimeout(() => {
        recalculateLayout()
        // 触发连接线重新渲染
        window.dispatchEvent(new CustomEvent("recalculateLayout"))
      }, 200)

      return () => clearTimeout(timer)
    }
  }, [nodeCount, recalculateLayout])

  // 单击选择节点（用于header工具栏）
  const selectNode = (nodeId: string) => {
    setSelectedNodeId(nodeId)

    // 隐藏样式面板（只有双击才显示）
    setShowStylePanel(null)

    // 如果点击的节点没有子节点，显示加号按钮
    const node = mindMapNodes[nodeId]
    if (node && node.children.length === 0) {
      setShowAddButton(nodeId)
    } else {
      setShowAddButton(null)
    }
  }

  // 开始编辑节点
  const handleStartEditNode = (nodeId: string) => {
    const node = mindMapNodes[nodeId]

    if (node) {
      console.log("开始编辑节点:", node.text, node.style)
      startEditNode(nodeId, node.text, node.style)
    }
  }

  // 保存节点编辑
  const handleSaveNodeEdit = () => {
    saveNodeEdit((text, style, richText) => {
      if (isEditingNode) {
        updateNode(isEditingNode, text, style, richText)
        // 延迟重新计算布局，等待DOM更新
        setTimeout(() => {
          recalculateLayout()
        }, 100)
      }
    })
  }

  // 样式更改处理
  const handleStyleChange = (nodeId: string, style: any) => {
    // 同时更新editingStyle和节点样式
    updateEditingStyle(style)
    updateNode(nodeId, mindMapNodes[nodeId].text, style)

    // 延迟重新计算布局，等待DOM更新
    setTimeout(() => {
      recalculateLayout()
      // 触发连接线重新渲染
      window.dispatchEvent(new CustomEvent("recalculateLayout"))
    }, 150)
  }

  // 添加子节点处理
  const handleAddChildNode = () => {
    if (selectedNodeId) {
      addChildNode(selectedNodeId)
      // 新节点会自动被选中，使用selectedNodeId来设置showAddButton
    }
  }

  //保留为空函数以避免重复处理
  const handleCanvasClick = () => {}

  // 处理节点折叠/展开
  const handleToggleCollapse = (nodeId: string) => {
    toggleNodeCollapse(nodeId)
    // 触发连接线重新渲染
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent("recalculateLayout"))
    }, 100)
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Top Header */}
      <Header activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Toolbar */}
      <Toolbar
        activeTab={activeTab}
        selectedNodeId={selectedNodeId}
        isEditingNode={isEditingNode}
        selectedNodeStyle={
          selectedNodeId ? mindMapNodes[selectedNodeId]?.style : undefined
        }
        onAddChildNode={handleAddChildNode}
        onAddSiblingNode={() => {
          if (selectedNodeId) {
            addSiblingNode(selectedNodeId)
          }
        }}
        onAddParentNode={() => {
          if (selectedNodeId) {
            addParentNode(selectedNodeId)
          }
        }}
        onToggleBold={styleHandlers.handleToggleBold}
        onToggleItalic={styleHandlers.handleToggleItalic}
        onColorChange={styleHandlers.handleColorChange}
        onFontFamilyChange={styleHandlers.handleFontFamilyChange}
        onFontSizeChange={styleHandlers.handleFontSizeChange}
        onTextAlignChange={styleHandlers.handleTextAlignChange}
        onBorderWidthChange={styleHandlers.handleBorderWidthChange}
        onBackgroundColorChange={styleHandlers.handleBackgroundColorChange}
      />

      {/* Main Content Area - Mind Map */}
      <MindMapCanvas
        mindMapNodes={mindMapNodes}
        selectedNodeId={selectedNodeId}
        isEditingNode={isEditingNode}
        editingText={editingText}
        editingStyle={editingStyle}
        showStylePanel={showStylePanel}
        showAddButton={showAddButton}
        isDragging={isDragging}
        viewOffset={viewOffset}
        onNodeSelect={selectNode}
        onNodeDoubleClick={handleStartEditNode}
        onNodeContextMenu={(_e, nodeId) => {
          setContextMenuNodeId(nodeId)
        }}
        onNodeMouseDown={handleNodeMouseDown}
        onEditingTextChange={(text, richText) => {
          setEditingText(text)
          if (richText) {
            setEditingRichText(richText)
          }
        }}
        onSaveEdit={handleSaveNodeEdit}
        onCancelEdit={cancelNodeEdit}
        onStyleChange={handleStyleChange}
        onAddChildNode={(nodeId) => {
          addChildNode(nodeId)
          setShowAddButton(null) // 添加节点后隐藏加号
        }}
        onCanvasClick={handleCanvasClick}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onToggleBold={styleHandlers.handleToggleBold}
        onToggleItalic={styleHandlers.handleToggleItalic}
        onToggleUnderline={styleHandlers.handleToggleUnderline}
        onColorChange={styleHandlers.handleColorChange}
        onFontSizeChange={styleHandlers.handleFontSizeChange}
        onTextAlignChange={styleHandlers.handleTextAlignChange}
        onToggleCollapse={handleToggleCollapse}
        onAddSiblingNode={addSiblingNode}
        onAddParentNode={addParentNode}
        onDeleteNode={deleteNode}
        onAIGenerate={handleAIGenerate}
      />
    </div>
  )
}
