import { useRef, useState } from "react"
import type { DragOffset, ViewOffset } from "../types/mindmap"

export const useViewDrag = () => {
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState<DragOffset>({ x: 0, y: 0 })
  const [viewOffset, setViewOffset] = useState<ViewOffset>({ x: 0, y: 0 })

  // 用于跟踪鼠标按下状态和位置
  const mouseDownRef = useRef<{
    isDown: boolean
    startX: number
    startY: number
    timestamp: number
  }>({ isDown: false, startX: 0, startY: 0, timestamp: 0 })

  // 拖拽阈值（像素）
  const DRAG_THRESHOLD = 5
  // 双击检测时间窗口（毫秒）
  const DOUBLE_CLICK_WINDOW = 300

  // 视图拖拽功能
  const handleNodeMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) {
      // 只响应左键
      e.stopPropagation()

      const now = Date.now()
      const timeSinceLastDown = now - mouseDownRef.current.timestamp

      // 如果在双击时间窗口内，不开始拖拽准备
      if (timeSinceLastDown < DOUBLE_CLICK_WINDOW) {
        mouseDownRef.current.isDown = false
        return
      }

      // 记录鼠标按下状态和位置，但不立即开始拖拽
      mouseDownRef.current = {
        isDown: true,
        startX: e.clientX,
        startY: e.clientY,
        timestamp: now
      }

      // 预设拖拽偏移，但不设置 isDragging
      setDragOffset({
        x: e.clientX - viewOffset.x,
        y: e.clientY - viewOffset.y
      })
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (mouseDownRef.current.isDown && !isDragging) {
      // 检查是否移动了足够的距离来开始拖拽
      const deltaX = Math.abs(e.clientX - mouseDownRef.current.startX)
      const deltaY = Math.abs(e.clientY - mouseDownRef.current.startY)

      if (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD) {
        // 开始拖拽
        setIsDragging(true)
      }
    }

    if (isDragging) {
      setViewOffset({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      })
    }
  }

  const handleMouseUp = () => {
    mouseDownRef.current.isDown = false
    setIsDragging(false)
  }

  return {
    isDragging,
    viewOffset,
    handleNodeMouseDown,
    handleMouseMove,
    handleMouseUp
  }
}
