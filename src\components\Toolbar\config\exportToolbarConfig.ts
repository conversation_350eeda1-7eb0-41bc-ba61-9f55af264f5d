import { BrainIcon } from "@/components/icons/BrainIcon"
import { Excelcon } from "@/components/icons/Excelcon"
import { OutlineContentIcon } from "@/components/icons/OutlineContentIcon"
import { PDFIcon } from "@/components/icons/PDFIcon"
import { PositionIcon } from "@/components/icons/PositionIcon"
import { PosonIcon } from "@/components/icons/PosonIcon"
import { PPTIcon } from "@/components/icons/PPTIcon"
import { ImageIcon } from "@radix-ui/react-icons"
import type { ToolbarItem } from "./startToolbarConfig"

// 视图工具栏配置
export const exportToolbarConfig: ToolbarItem[] = [
  // 节点样式组
  {
    type: "button",
    id: "image",
    label: "图片",
    icon: ImageIcon,
    text: "图片",
    className: "toolbar-text-btn",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "pdf",
    label: "PDF",
    icon: PDFIcon,
    text: "PDF",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "outlinecontent",
    label: "大纲",
    icon: OutlineContentIcon,
    text: "大纲",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "position",
    label: "POS",
    icon: PositionIcon,
    text: "POS",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "excel",
    label: "Excel",
    icon: Excelcon,
    text: "Excel",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "excel",
    label: "FreeMind",
    icon: BrainIcon,
    text: "FreeMind",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "ppt",
    label: "PPT大纲文件",
    icon: PPTIcon,
    text: "PPT大纲文件",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "posm",
    label: "全部画布POSM文件",
    icon: PosonIcon,
    text: "全部画布POSM文件",
    disabled: "!isEnabled"
  }
]
