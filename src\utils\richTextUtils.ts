import type { RichTextContent, TextSegment, TextStyle } from "@/types/mindmap"

// 将普通文本转换为富文本格式
export const textToRichText = (text: string): RichTextContent => {
  return {
    segments: [{ text }]
  }
}

// 将富文本转换为普通文本
export const richTextToText = (richText: RichTextContent): string => {
  return richText.segments.map(segment => segment.text).join("")
}

// 获取文本选择范围在富文本中的位置
export const getSelectionInRichText = (
  richText: RichTextContent,
  selectionStart: number,
  selectionEnd: number
): { startSegmentIndex: number; startOffset: number; endSegmentIndex: number; endOffset: number } => {
  let currentPosition = 0
  let startSegmentIndex = -1
  let startOffset = 0
  let endSegmentIndex = -1
  let endOffset = 0

  for (let i = 0; i < richText.segments.length; i++) {
    const segment = richText.segments[i]
    const segmentLength = segment.text.length

    // 查找开始位置
    if (startSegmentIndex === -1 && currentPosition + segmentLength >= selectionStart) {
      startSegmentIndex = i
      startOffset = selectionStart - currentPosition
    }

    // 查找结束位置
    if (currentPosition + segmentLength >= selectionEnd) {
      endSegmentIndex = i
      endOffset = selectionEnd - currentPosition
      break
    }

    currentPosition += segmentLength
  }

  return { startSegmentIndex, startOffset, endSegmentIndex, endOffset }
}

// 应用样式到选中的文本范围
export const applyStyleToSelection = (
  richText: RichTextContent,
  selectionStart: number,
  selectionEnd: number,
  style: TextStyle
): RichTextContent => {
  if (selectionStart === selectionEnd) {
    return richText // 没有选中文本，不做任何操作
  }

  const { startSegmentIndex, startOffset, endSegmentIndex, endOffset } = 
    getSelectionInRichText(richText, selectionStart, selectionEnd)

  if (startSegmentIndex === -1 || endSegmentIndex === -1) {
    return richText // 无效的选择范围
  }

  const newSegments: TextSegment[] = []

  for (let i = 0; i < richText.segments.length; i++) {
    const segment = richText.segments[i]

    if (i < startSegmentIndex || i > endSegmentIndex) {
      // 不在选择范围内的片段，直接复制
      newSegments.push({ ...segment })
    } else if (i === startSegmentIndex && i === endSegmentIndex) {
      // 选择范围在同一个片段内
      const beforeText = segment.text.substring(0, startOffset)
      const selectedText = segment.text.substring(startOffset, endOffset)
      const afterText = segment.text.substring(endOffset)

      // 添加选择前的文本
      if (beforeText) {
        newSegments.push({ text: beforeText, style: segment.style })
      }

      // 添加选中的文本（应用新样式）
      if (selectedText) {
        newSegments.push({ 
          text: selectedText, 
          style: { ...segment.style, ...style }
        })
      }

      // 添加选择后的文本
      if (afterText) {
        newSegments.push({ text: afterText, style: segment.style })
      }
    } else if (i === startSegmentIndex) {
      // 选择范围的开始片段
      const beforeText = segment.text.substring(0, startOffset)
      const selectedText = segment.text.substring(startOffset)

      if (beforeText) {
        newSegments.push({ text: beforeText, style: segment.style })
      }
      if (selectedText) {
        newSegments.push({ 
          text: selectedText, 
          style: { ...segment.style, ...style }
        })
      }
    } else if (i === endSegmentIndex) {
      // 选择范围的结束片段
      const selectedText = segment.text.substring(0, endOffset)
      const afterText = segment.text.substring(endOffset)

      if (selectedText) {
        newSegments.push({ 
          text: selectedText, 
          style: { ...segment.style, ...style }
        })
      }
      if (afterText) {
        newSegments.push({ text: afterText, style: segment.style })
      }
    } else {
      // 完全在选择范围内的片段
      newSegments.push({ 
        text: segment.text, 
        style: { ...segment.style, ...style }
      })
    }
  }

  return { segments: newSegments }
}

// 合并相邻的相同样式片段
export const mergeAdjacentSegments = (richText: RichTextContent): RichTextContent => {
  if (richText.segments.length <= 1) {
    return richText
  }

  const mergedSegments: TextSegment[] = []
  let currentSegment = { ...richText.segments[0] }

  for (let i = 1; i < richText.segments.length; i++) {
    const nextSegment = richText.segments[i]
    
    // 检查样式是否相同
    const stylesEqual = JSON.stringify(currentSegment.style || {}) === 
                       JSON.stringify(nextSegment.style || {})

    if (stylesEqual) {
      // 合并文本
      currentSegment.text += nextSegment.text
    } else {
      // 样式不同，保存当前片段并开始新的片段
      mergedSegments.push(currentSegment)
      currentSegment = { ...nextSegment }
    }
  }

  // 添加最后一个片段
  mergedSegments.push(currentSegment)

  return { segments: mergedSegments }
}

// 渲染富文本为HTML字符串
export const renderRichTextToHTML = (richText: RichTextContent): string => {
  return richText.segments.map(segment => {
    let html = segment.text
    
    if (segment.style) {
      const styles: string[] = []
      
      if (segment.style.fontWeight === "bold") {
        html = `<strong>${html}</strong>`
      }
      
      if (segment.style.fontStyle === "italic") {
        html = `<em>${html}</em>`
      }
      
      if (segment.style.textDecoration === "underline") {
        html = `<u>${html}</u>`
      }
      
      if (segment.style.color) {
        styles.push(`color: ${segment.style.color}`)
      }
      
      if (segment.style.backgroundColor) {
        styles.push(`background-color: ${segment.style.backgroundColor}`)
      }
      
      if (styles.length > 0) {
        html = `<span style="${styles.join('; ')}">${html}</span>`
      }
    }
    
    return html
  }).join("")
}

// 从HTML元素中获取当前选择范围的文本位置
export const getSelectionRange = (element: HTMLElement): { start: number; end: number } => {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) {
    return { start: 0, end: 0 }
  }

  const range = selection.getRangeAt(0)
  const preCaretRange = range.cloneRange()
  preCaretRange.selectNodeContents(element)
  preCaretRange.setEnd(range.startContainer, range.startOffset)
  const start = preCaretRange.toString().length

  const end = start + range.toString().length

  return { start, end }
}
