import type { RichTextContent, TextStyle } from "@/types/mindmap"
import { 
  applyStyleToSelection, 
  getSelectionRange, 
  mergeAdjacentSegments, 
  renderRichTextToHTML, 
  richTextToText, 
  textToRichText 
} from "@/utils/richTextUtils"
import { useCallback, useEffect, useRef, useState } from "react"

interface RichTextEditorProps {
  value: string
  richText?: RichTextContent
  onChange: (text: string, richText: RichTextContent) => void
  onStyleApply?: (style: TextStyle) => void
  style?: React.CSSProperties
  className?: string
  onKeyDown?: (e: React.KeyboardEvent) => void
  onBlur?: () => void
  placeholder?: string
}

export const RichTextEditor = ({
  value,
  richText,
  onChange,
  onStyleApply,
  style,
  className,
  onKeyDown,
  onBlur,
  placeholder
}: RichTextEditorProps) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const [currentRichText, setCurrentRichText] = useState<RichTextContent>(
    richText || textToRichText(value)
  )
  const [lastSelectionRange, setLastSelectionRange] = useState({ start: 0, end: 0 })

  // 保存当前选择范围
  const saveSelection = useCallback(() => {
    if (editorRef.current) {
      const range = getSelectionRange(editorRef.current)
      setLastSelectionRange(range)
    }
  }, [])

  // 恢复选择范围
  const restoreSelection = useCallback((start: number, end: number) => {
    if (!editorRef.current) return

    const selection = window.getSelection()
    if (!selection) return

    let charIndex = 0
    const walker = document.createTreeWalker(
      editorRef.current,
      NodeFilter.SHOW_TEXT,
      null
    )

    const range = document.createRange()
    let startSet = false
    let endSet = false

    let node
    while ((node = walker.nextNode()) && !endSet) {
      const textNode = node as Text
      const textLength = textNode.textContent?.length || 0
      const nextCharIndex = charIndex + textLength

      if (!startSet && start <= nextCharIndex) {
        range.setStart(textNode, Math.min(start - charIndex, textLength))
        startSet = true
      }

      if (startSet && end <= nextCharIndex) {
        range.setEnd(textNode, Math.min(end - charIndex, textLength))
        endSet = true
      }

      charIndex = nextCharIndex
    }

    if (startSet) {
      selection.removeAllRanges()
      selection.addRange(range)
    }
  }, [])

  // 应用样式到当前选择
  const applyStyleToCurrentSelection = useCallback((styleToApply: TextStyle) => {
    const { start, end } = lastSelectionRange
    
    if (start === end) {
      // 没有选中文本，不应用样式
      return
    }

    const newRichText = applyStyleToSelection(currentRichText, start, end, styleToApply)
    const mergedRichText = mergeAdjacentSegments(newRichText)
    
    setCurrentRichText(mergedRichText)
    
    // 更新编辑器内容
    if (editorRef.current) {
      editorRef.current.innerHTML = renderRichTextToHTML(mergedRichText)
      
      // 恢复选择范围
      setTimeout(() => {
        restoreSelection(start, end)
      }, 0)
    }

    // 通知父组件
    const newText = richTextToText(mergedRichText)
    onChange(newText, mergedRichText)
    
    if (onStyleApply) {
      onStyleApply(styleToApply)
    }
  }, [currentRichText, lastSelectionRange, onChange, onStyleApply, restoreSelection])

  // 处理文本输入
  const handleInput = useCallback(() => {
    if (!editorRef.current) return

    const text = editorRef.current.textContent || ""
    const newRichText = textToRichText(text)
    
    setCurrentRichText(newRichText)
    onChange(text, newRichText)
  }, [onChange])

  // 处理选择变化
  const handleSelectionChange = useCallback(() => {
    saveSelection()
  }, [saveSelection])

  // 初始化编辑器内容
  useEffect(() => {
    if (editorRef.current && richText) {
      const html = renderRichTextToHTML(richText)
      if (editorRef.current.innerHTML !== html) {
        editorRef.current.innerHTML = html
      }
      setCurrentRichText(richText)
    }
  }, [richText])

  // 监听选择变化
  useEffect(() => {
    document.addEventListener("selectionchange", handleSelectionChange)
    return () => {
      document.removeEventListener("selectionchange", handleSelectionChange)
    }
  }, [handleSelectionChange])

  // 暴露应用样式的方法给父组件
  useEffect(() => {
    if (editorRef.current) {
      // 将方法绑定到编辑器元素上，供外部调用
      ;(editorRef.current as any).applyStyle = applyStyleToCurrentSelection
    }
  }, [applyStyleToCurrentSelection])

  return (
    <div
      ref={editorRef}
      contentEditable
      suppressContentEditableWarning
      style={{
        outline: "none",
        minHeight: "1.2em",
        ...style
      }}
      className={className}
      onInput={handleInput}
      onKeyDown={onKeyDown}
      onBlur={onBlur}
      onMouseUp={saveSelection}
      onKeyUp={saveSelection}
      data-placeholder={placeholder}
    />
  )
}

// 导出应用样式的辅助函数
export const applyStyleToEditor = (editorElement: HTMLElement, style: TextStyle) => {
  if ((editorElement as any).applyStyle) {
    ;(editorElement as any).applyStyle(style)
  }
}
