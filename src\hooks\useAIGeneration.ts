import type { NodeStyle } from "../types/mindmap"

export const useAIGeneration = (
  updateNode: (nodeId: string, text: string, style?: NodeStyle) => void,
  createNodeFast: (
    id: string,
    text: string,
    level: number,
    parentId: string
  ) => Promise<void>,
  recalculateLayout: () => void,
  clearNodeChildren: (nodeId: string) => void
) => {
  const handleAIGenerate = async (nodeId: string, content: string) => {
    try {
      // 首先清除当前节点的所有子节点（如果有的话）
      clearNodeChildren(nodeId)

      // 立即更新当前节点显示正在生成的状态
      updateNode(nodeId, `${content} (AI生成中...)`)

      const { generateMindMapStream } = await import("../services/aiService")

      // 用于跟踪已创建的节点，避免重复创建
      const createdNodes = new Set<string>()
      // 将当前触发AI的节点标记为已存在
      createdNodes.add(nodeId)

      // 用于暂存等待父节点创建的节点
      const pendingNodes: Array<{
        id: string
        text: string
        level: number
        parentId: string
      }> = []

      // 尝试处理待处理队列中的节点
      const processPendingNodes = () => {
        const nodesToProcess = []

        // 找出所有可以立即创建的节点（父节点已存在）
        for (let i = pendingNodes.length - 1; i >= 0; i--) {
          const node = pendingNodes[i]
          if (createdNodes.has(node.parentId)) {
            nodesToProcess.push(node)
            pendingNodes.splice(i, 1) // 从待处理队列中移除
          }
        }

        // 立即创建这些节点
        nodesToProcess.forEach(async (node) => {
          await createNodeFast(node.id, node.text, node.level, node.parentId)
          createdNodes.add(node.id)

          // 立即触发布局重新计算
          recalculateLayout()
          window.dispatchEvent(new CustomEvent("recalculateLayout"))
        })

        // 如果有节点被创建，再次检查待处理队列
        if (nodesToProcess.length > 0) {
          // 使用 setTimeout 避免深度递归，但延迟很短
          setTimeout(processPendingNodes, 10)
        }
      }

      // 处理单个节点的函数
      const processNode = async (node: {
        id: string
        text: string
        level: number
        parentId: string
      }) => {
        // 检查父节点是否已存在
        if (createdNodes.has(node.parentId)) {
          // 立即创建节点
          await createNodeFast(node.id, node.text, node.level, node.parentId)
          createdNodes.add(node.id)

          // 立即触发布局重新计算
          recalculateLayout()
          window.dispatchEvent(new CustomEvent("recalculateLayout"))

          // 尝试处理待处理队列中的节点
          processPendingNodes()
        } else {
          // 父节点还未创建，暂存到待处理队列
          pendingNodes.push(node)
        }
      }

      generateMindMapStream(
        content,
        async (data) => {
          if (data.type === "node" && data.node) {
            const aiNode = data.node

            if (aiNode.id === "root") {
              // 如果AI返回的是根节点，更新当前触发AI的节点
              updateNode(nodeId, aiNode.text)
              // 确保当前节点在已创建集合中
              createdNodes.add(nodeId)
            } else {
              // 非根节点，需要调整父子关系
              let adjustedParentId = aiNode.parentId

              // 如果AI返回的父节点是"root"，将其改为当前触发AI的节点
              if (aiNode.parentId === "root") {
                adjustedParentId = nodeId
              }

              // 立即尝试创建节点
              await processNode({
                id: aiNode.id,
                text: aiNode.text,
                level: aiNode.level,
                parentId: adjustedParentId
              })
            }
          }
        },
        async () => {
          // 流式数据接收完成，处理剩余的待创建节点
          if (pendingNodes.length > 0) {
            console.warn("仍有未处理的节点，可能存在父节点缺失的情况:", pendingNodes)

            // 按层级排序，尝试创建剩余节点
            pendingNodes.sort((a, b) => a.level - b.level)

            for (const node of pendingNodes) {
              // 如果父节点仍不存在，将其父节点设为当前触发AI的节点
              const parentId = createdNodes.has(node.parentId) ? node.parentId : nodeId
              await createNodeFast(node.id, node.text, node.level, parentId)
              createdNodes.add(node.id)
            }
          }

          // 生成完成，移除"生成中"状态
          updateNode(nodeId, content)

          // 最终布局重新计算
          setTimeout(() => {
            recalculateLayout()
            window.dispatchEvent(new CustomEvent("recalculateLayout"))
          }, 200)

          console.log(`节点 ${nodeId} 的AI思维导图生成完成`)
        },
        (err) => {
          alert("AI流式生成失败：" + err)
        }
      )
    } catch (error) {
      console.error("AI流式生成过程中出错:", error)
    }
  }

  return { handleAIGenerate }
}
