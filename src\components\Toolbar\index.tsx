import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"

import type { ToolbarProps } from "./type/types"
import { ExportToolbar } from "./views/ExportToolbar"
import { InsertToolbar } from "./views/InsertToolbar"
import { StartToolbar } from "./views/StartToolbar"
import { StyleToolbar } from "./views/StyleToolbar"
import { ViewToolbar } from "./views/ViewToolbar"

const Toolbar: React.FC<ToolbarProps> = (props) => {
  const {
    activeTab,
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onAddSiblingNode,
    onAddParentNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
    onBackgroundColorChange
  } = props

  const sectionProps = {
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onAddSiblingNode,
    onAddParentNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
    onBackgroundColorChange
  }

  const renderToolbar = () => {
    switch (activeTab) {
      case "开始":
        return <StartToolbar {...sectionProps} />
      case "样式":
        return <StyleToolbar {...sectionProps} />
      case "导出":
        return <ExportToolbar selectedNodeId={null} />
      case "插入":
        return <InsertToolbar {...sectionProps} />
      case "视图":
        return <ViewToolbar {...sectionProps} />
      default:
        return <div className="toolbar">空</div>
    }
  }

  return (
    <Tooltip.Provider delayDuration={100}>{renderToolbar()}</Tooltip.Provider>
  )
}

export default Toolbar
