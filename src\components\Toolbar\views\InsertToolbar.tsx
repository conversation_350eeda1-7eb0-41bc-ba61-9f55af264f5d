import React from "react"

import type { ToolbarSectionProps } from "../type/types"
import { ToolbarRenderer } from "../utils/ToolbarRenderer"

import { insertToolbarConfig } from "../config/insertToolbarConfig"
import "../../common/css/header.css"

export const InsertToolbar: React.FC<ToolbarSectionProps> = (props) => {
  return (
    <ToolbarRenderer
      {...props}
      config={insertToolbarConfig}
      ariaLabel="插入工具栏"
      separatorClassName="toolbar-separator-vertical"
    />
  )
}
