import React from "react"

import type { ToolbarSectionProps } from "../type/types"
import { ToolbarRenderer } from "../utils/ToolbarRenderer"

import "../../common/css/header.css"
import { viewToolbarConfig } from "../config/viewToolbarConfig"

export const ViewToolbar: React.FC<ToolbarSectionProps> = (props) => {
  return (
    <ToolbarRenderer
      {...props}
      config={viewToolbarConfig}
      ariaLabel="插入工具栏"
      separatorClassName="toolbar-separator-vertical"
    />
  )
}
