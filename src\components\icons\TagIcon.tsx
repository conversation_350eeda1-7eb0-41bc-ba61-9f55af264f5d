// 标签图标
export const TagIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    style={{ transform: "scale(1.1)" }}
  >
    <path
      d="M448 853.333333L170.666667 576 576 170.666667H853.333333v277.333333L448 853.333333z m0-119.466666L768 426.666667V256h-170.666667l-307.2 320 157.866667 157.866667zM682.666667 298.666667c25.6 0 42.666667 17.066667 42.666666 42.666666s-17.066667 42.666667-42.666666 42.666667-42.666667-17.066667-42.666667-42.666667 17.066667-42.666667 42.666667-42.666666z"
      fill="#707070"
    />
  </svg>
)
